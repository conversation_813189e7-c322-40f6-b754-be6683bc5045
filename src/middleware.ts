import { NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'
import { enforceUsageLimitsEdge } from '@/lib/middleware/usage-limiter-edge'

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // SECURITY: Block debug/test endpoints in production
  const debugEndpoints = ['/api/test-env', '/api/debug-users', '/api/setup-epic9-schema', '/api/setup-epic5-schema']
  if (process.env.NODE_ENV === 'production' && debugEndpoints.some(endpoint =>
    request.nextUrl.pathname.startsWith(endpoint))) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 })
  }

  // Skip middleware for n8n config endpoint (needs to be accessible without auth)
  if (request.nextUrl.pathname === '/api/n8n/config') {
    return NextResponse.next()
  }

  // First, enforce usage limits for API routes that consume usage
  const usageLimitResponse = await enforceUsageLimitsEdge(request)
  if (usageLimitResponse) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚫 Usage limit exceeded for:', request.nextUrl.pathname)
    }
    return usageLimitResponse
  }

  // Handle Supabase authentication and session management
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
