import axios from 'axios';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';
import { topicExtractor } from '../utils/topicExtractor.js';
import { geocodingService } from './geocoding.js';
import { cacheService } from './cache.js';
import { smartPropertyAgent } from './smartPropertyAgent.js';

export interface ResearchRequest {
  address: string;
  query: string;
  userId?: string;
  apiKeyId?: string;
}

export interface ResearchResult {
  success: boolean;
  jurisdiction: string;
  topic: string;
  answer: string;
  sources: string[];
  confidence: number;
  cached: boolean;
  processingTimeMs: number;
  costUsd: number;
  method: 'perplexity-gemini' | 'perplexity-only' | 'smart-agent' | 'cache';
}

export interface PerplexityResponse {
  id: string;
  model: string;
  object: string;
  created: number;
  choices: Array<{
    index: number;
    finish_reason: string;
    message: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  citations?: string[] | Record<string, any>;
  search_results?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
}

class MunicipalResearchService {
  private perplexityApiKey: string;
  private geminiApiKey: string;

  constructor() {
    this.perplexityApiKey = config.ai.perplexity.apiKey;
    this.geminiApiKey = config.ai.gemini.apiKey;
  }

  /**
   * Main research method - orchestrates the research process with smart agent first
   */
  async performResearch(request: ResearchRequest): Promise<ResearchResult> {
    const startTime = Date.now();
    logger.info(`🔬 Starting municipal research: "${request.query}" for ${request.address}`);

    try {
      // STEP 1: Try Smart Property Agent first for property-related queries
      logger.info(`🤖 Checking if Smart Property Agent can handle query...`);
      const smartAgentResult = await smartPropertyAgent.tryHandleQuery({
        address: request.address,
        query: request.query,
        userId: request.userId,
        apiKeyId: request.apiKeyId
      });

      if (smartAgentResult.canHandle && smartAgentResult.response) {
        logger.info(`✅ Smart Property Agent handled query successfully`);
        return smartAgentResult.response;
      }

      logger.info(`🤖 Smart Property Agent cannot handle query: ${smartAgentResult.fallbackReason}`);
      logger.info(`🔄 Falling back to traditional Perplexity research...`);

      // STEP 2: Fallback to traditional research flow
      // Get jurisdiction from address
      const jurisdiction = await geocodingService.getJurisdiction(request.address);
      logger.info(`🏛️ Identified jurisdiction: ${jurisdiction}`);

      // Extract topic from query
      const topic = await topicExtractor.extractTopic(request.query);
      logger.info(`🏷️ Extracted topic: ${topic}`);

      // Check cache first
      const cachedResult = await cacheService.getCachedResearch(topic, jurisdiction);
      if (cachedResult) {
        logger.info(`✅ Cache hit for ${topic} in ${jurisdiction}`);
        return {
          success: true,
          jurisdiction,
          topic,
          answer: cachedResult.answer,
          sources: cachedResult.sources,
          confidence: cachedResult.confidence,
          cached: true,
          processingTimeMs: Date.now() - startTime,
          costUsd: 0,
          method: 'cache'
        };
      }

      // Perform fresh research using Perplexity only
      logger.info(`🔍 Performing fresh research for ${topic} in ${jurisdiction}`);
      const researchResult = await this.perplexityOnlyResearch(jurisdiction, request.query, topic);

      // Cache the result
      await cacheService.storeResearch(topic, jurisdiction, researchResult);

      const totalTime = Date.now() - startTime;
      logger.info(`✅ Research completed in ${totalTime}ms with confidence ${researchResult.confidence}`);

      return {
        success: true,
        jurisdiction,
        topic,
        answer: researchResult.answer,
        sources: researchResult.sources,
        confidence: researchResult.confidence,
        cached: false,
        processingTimeMs: totalTime,
        costUsd: researchResult.costUsd,
        method: 'perplexity-only'
      };

    } catch (error) {
      logger.error('Municipal research failed:', error);
      throw new Error(`Research failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Two-stage research pipeline: Perplexity for data collection + Gemini for processing
   */
  private async twoStageResearch(jurisdiction: string, query: string, topic: string) {
    logger.info(`📊 Stage 1: Collecting raw data for ${jurisdiction}`);
    
    // Stage 1: Use Perplexity to collect comprehensive municipal data
    const rawData = await this.collectRawDataWithPerplexity(jurisdiction, query);
    
    logger.info(`🧠 Stage 2: Processing data with AI for final answer`);
    
    // Stage 2: Use OpenAI (Gemini-like) to process and format the answer
    const processedResult = await this.processDataWithAI(rawData, jurisdiction, query, topic);
    
    return processedResult;
  }

  /**
   * Perplexity-only research pipeline: Skip Gemini processing, use raw Perplexity response
   */
  private async perplexityOnlyResearch(jurisdiction: string, query: string, topic: string) {
    logger.info(`📊 Using Perplexity-only research for ${jurisdiction}`);

    // Use Perplexity to collect municipal data
    const rawData = await this.collectRawDataWithPerplexity(jurisdiction, query);

    // Calculate confidence based on answer quality (no AI processing)
    const confidence = this.calculateAnswerConfidence(rawData.content, rawData.sources);

    logger.info(`📊 Perplexity-only research completed with confidence ${confidence}`);

    return {
      answer: rawData.content,
      sources: rawData.sources,
      confidence,
      costUsd: rawData.cost
    };
  }

  /**
   * Stage 1: Use Perplexity to collect raw municipal data
   */
  private async collectRawDataWithPerplexity(jurisdiction: string, query: string) {
    const searchQuery = `${jurisdiction} municipal ordinance regulations ${query} official sources`;
    
    const requestData = {
      model: "sonar",
      messages: [
        {
          role: "system",
          content: "You are a helpful municipal compliance assistant. Answer questions in a conversational, friendly tone like ChatGPT or Gemini. Include specific ordinance citations and requirements, but explain them naturally without formal structure."
        },
        {
          role: "user",
          content: `I need help with municipal regulations in ${jurisdiction} for: ${query}

Please give me a conversational answer that includes:
- The specific requirements and measurements
- Exact ordinance section numbers (like "per Section 64-3.2" or "Chapter 64, Article 3")
- Contact info for the relevant department
- Any permit requirements

Keep it friendly and conversational - like you're explaining it to a neighbor, but make sure to include the specific legal citations and requirements.`
        }
      ],
      max_tokens: 4000,
      temperature: 0.1,
      stream: false
    };

    try {
      const response = await axios.post(
        'https://api.perplexity.ai/chat/completions',
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${this.perplexityApiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const result = response.data as PerplexityResponse;
      const content = result.choices[0]?.message?.content || '';

      // DEBUG: Log the full response structure to understand where sources are
      console.log('🔍 DEBUG: Perplexity response structure:', JSON.stringify({
        hasChoices: !!result.choices,
        choicesLength: result.choices?.length,
        hasMessage: !!result.choices?.[0]?.message,
        messageKeys: result.choices?.[0]?.message ? Object.keys(result.choices[0].message) : [],
        hasCitations: !!result.citations,
        citationsType: typeof result.citations,
        citationsLength: Array.isArray(result.citations) ? result.citations.length : 'not array',
        topLevelKeys: Object.keys(result)
      }, null, 2));

      // Extract sources from Perplexity response - check multiple locations
      let sources = this.extractSourcesFromContent(content);

      // Extract sources from Perplexity citations
      if (sources.length === 0 && result.citations) {
        if (Array.isArray(result.citations)) {
          // Citations are direct URLs in array format
          sources = result.citations.filter(Boolean);
        } else if (typeof result.citations === 'object') {
          // Handle citations as object with numbered keys
          sources = Object.values(result.citations).filter(Boolean);
        }
      }

      // Calculate cost
      const inputTokens = result.usage?.prompt_tokens || 0;
      const outputTokens = result.usage?.completion_tokens || 0;
      const cost = this.calculatePerplexityCost(inputTokens, outputTokens);

      // 🔍 DEBUG: Log Perplexity raw response structure
      console.log("🔍 PERPLEXITY RAW RESPONSE:", JSON.stringify({
        citations: result.citations,
        citationsType: typeof result.citations,
        citationsKeys: result.citations ? Object.keys(result.citations) : null,
        hasSearchResults: !!result.search_results,
        searchResultsLength: result.search_results?.length,
        extractedSourcesCount: sources.length,
        extractedSources: sources
      }, null, 2));

      logger.info(`📊 Perplexity research completed: ${inputTokens} input + ${outputTokens} output tokens, cost: $${cost.toFixed(4)}, sources: ${sources.length}`);

      return {
        content,
        sources,
        inputTokens,
        outputTokens,
        cost
      };

    } catch (error) {
      logger.error('Perplexity API error:', error);
      throw new Error(`Perplexity research failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Stage 2: Process raw data with AI to create final answer
   */
  private async processDataWithAI(rawData: any, jurisdiction: string, query: string, topic: string) {
    try {
      const prompt = `You are a helpful municipal research assistant. Take this verbose research data and create a clear, conversational answer that directly answers the user's question.

JURISDICTION: ${jurisdiction}
QUERY: ${query}
TOPIC: ${topic}

RAW RESEARCH DATA:
${rawData.content}

Create a concise, conversational response that:
- Directly answers the question with specific measurements/requirements
- Includes the relevant ordinance citation (e.g., "per Section X.Y")
- Mentions contact info if available
- Is easy to read and understand
- Avoids formal memo language or excessive structure

Keep it friendly and conversational, like you're explaining it to a neighbor.`;

      // 🧠 DEBUG: Log Gemini input
      console.log("🧠 GEMINI INPUT:", {
        promptLength: prompt.length,
        sourcesFound: rawData.sources.length,
        sources: rawData.sources,
        jurisdiction,
        query
      });

      const response = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
        {
          contents: [
            {
              parts: [
                {
                  text: `You are a helpful assistant that explains municipal regulations in simple, conversational language.\n\n${prompt}`
                }
              ]
            }
          ],
          generationConfig: {
            maxOutputTokens: config.ai.gemini.maxTokens,
            temperature: config.ai.gemini.temperature
          }
        },
        {
          headers: {
            'x-goog-api-key': this.geminiApiKey,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const processedAnswer = response.data.candidates?.[0]?.content?.parts?.[0]?.text || rawData.content;

      // 🧠 DEBUG: Log Gemini response
      console.log("🧠 GEMINI RESPONSE:", {
        success: !!response.data,
        hasContent: !!response.data.candidates?.[0]?.content,
        error: response.data.error,
        candidatesLength: response.data.candidates?.length,
        processedAnswerLength: processedAnswer.length
      });

      // Calculate processing cost
      const inputTokens = response.data.usageMetadata?.promptTokenCount || 0;
      const outputTokens = response.data.usageMetadata?.candidatesTokenCount || 0;
      const processingCost = this.calculateGeminiCost(inputTokens, outputTokens);

      // Calculate confidence based on answer quality
      const confidence = this.calculateAnswerConfidence(processedAnswer, rawData.sources);

      logger.info(`🧠 AI processing completed: ${inputTokens} input + ${outputTokens} output tokens, cost: $${processingCost.toFixed(4)}`);

      return {
        answer: processedAnswer,
        sources: rawData.sources,
        confidence,
        costUsd: rawData.cost + processingCost
      };

    } catch (error) {
      logger.error('AI processing error:', error);
      // Fallback to raw data with lower confidence
      return {
        answer: rawData.content,
        sources: rawData.sources,
        confidence: 0.65,
        costUsd: rawData.cost
      };
    }
  }

  /**
   * Calculate Gemini API costs
   */
  private calculateGeminiCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (inputTokens / 1000000) * config.pricing.gemini.inputTokenCost;
    const outputCost = (outputTokens / 1000000) * config.pricing.gemini.outputTokenCost;
    return inputCost + outputCost;
  }

  /**
   * Calculate answer confidence based on content quality
   */
  private calculateAnswerConfidence(answer: string, sources: string[]): number {
    let confidence = 0.5; // Base confidence

    // Check answer length and structure
    if (answer.length > 500) confidence += 0.1;
    if (answer.includes('ordinance') || answer.includes('regulation')) confidence += 0.1;
    if (answer.includes('section') || answer.includes('§')) confidence += 0.1;

    // Check source quality
    if (sources.length >= 3) confidence += 0.1;
    if (sources.some(source => source.includes('.gov'))) confidence += 0.1;

    // Check for specific municipal information
    if (answer.includes('permit') || answer.includes('application')) confidence += 0.05;
    if (answer.includes('contact') || answer.includes('phone')) confidence += 0.05;

    return Math.min(confidence, 1.0); // Cap at 1.0
  }

  /**
   * Calculate Perplexity API costs
   */
  private calculatePerplexityCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (inputTokens / 1000000) * config.pricing.perplexity.inputTokenCost;
    const outputCost = (outputTokens / 1000000) * config.pricing.perplexity.outputTokenCost;
    return inputCost + outputCost + config.pricing.perplexity.requestCost;
  }

  /**
   * Extract source URLs from Perplexity response
   */
  private extractSourcesFromContent(content: string): string[] {
    const urlRegex = /https?:\/\/[^\s\)]+/g;
    const urls = content.match(urlRegex) || [];
    return [...new Set(urls)]; // Remove duplicates
  }
}

export const municipalResearchService = new MunicipalResearchService();
