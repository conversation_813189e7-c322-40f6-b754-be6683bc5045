import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiKey } from '../middleware/auth.js';
import { asyncHandler, ValidationError, ForbiddenError } from '../middleware/errorHandler.js';
import { usageTracker } from '../services/usageTracker.js';
import { cacheService } from '../services/cache.js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const router = Router();
const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

// Admin middleware - check if API key has admin access
const requireAdmin = (req: Request, res: Response, next: any) => {
  const apiKeyData = req.apiKey;
  
  if (!apiKeyData || 
      (!apiKeyData.allowed_endpoints.includes('admin') && 
       !apiKeyData.allowed_endpoints.includes('*'))) {
    throw new ForbiddenError('Admin access required');
  }
  
  next();
};

/**
 * GET /api/v1/admin/stats
 * Get comprehensive system statistics
 */
router.get('/stats',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const days = parseInt(req.query.days as string) || 30;

    // Get overall usage statistics
    const overallStats = await usageTracker.getAllUsageStats(days);
    
    // Get cache statistics
    const cacheStats = await cacheService.getCacheStats();

    // Get API key statistics
    const { data: apiKeys, error: apiKeysError } = await supabase
      .from('api_keys')
      .select('id, name, is_active, usage_count, created_at, last_used_at')
      .order('usage_count', { ascending: false });

    if (apiKeysError) {
      logger.error('Admin stats - API keys error:', apiKeysError);
    }

    // Get trial code statistics
    const { data: trialCodes, error: trialError } = await supabase
      .from('trial_codes')
      .select('id, code, email, is_active, requests_used, max_requests, activated_at, created_at')
      .order('created_at', { ascending: false });

    if (trialError) {
      logger.error('Admin stats - trial codes error:', trialError);
    }

    const systemStats = {
      overview: {
        totalApiKeys: apiKeys?.length || 0,
        activeApiKeys: apiKeys?.filter(key => key.is_active).length || 0,
        totalTrialCodes: trialCodes?.length || 0,
        activatedTrials: trialCodes?.filter(code => code.activated_at).length || 0
      },
      usage: overallStats,
      cache: cacheStats,
      apiKeys: {
        total: apiKeys?.length || 0,
        active: apiKeys?.filter(key => key.is_active).length || 0,
        topUsers: apiKeys?.slice(0, 10).map(key => ({
          id: key.id,
          name: key.name,
          usageCount: key.usage_count || 0,
          lastUsed: key.last_used_at,
          createdAt: key.created_at
        })) || []
      },
      trials: {
        total: trialCodes?.length || 0,
        activated: trialCodes?.filter(code => code.activated_at).length || 0,
        totalRequests: trialCodes?.reduce((sum, code) => sum + (code.requests_used || 0), 0) || 0,
        recent: trialCodes?.slice(0, 10).map(code => ({
          code: code.code,
          email: code.email,
          requestsUsed: code.requests_used || 0,
          maxRequests: code.max_requests,
          activated: !!code.activated_at,
          createdAt: code.created_at
        })) || []
      }
    };

    res.json({
      success: true,
      data: systemStats,
      meta: {
        period: `${days} days`,
        timestamp: new Date().toISOString()
      }
    });
  })
);

/**
 * GET /api/v1/admin/api-keys
 * Get all API keys with usage information
 */
router.get('/api-keys',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = (page - 1) * limit;

    const { data: apiKeys, error, count } = await supabase
      .from('api_keys')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error('Failed to fetch API keys');
    }

    // Get usage stats for each API key
    const apiKeysWithStats = await Promise.all(
      (apiKeys || []).map(async (key) => {
        const stats = await usageTracker.getUsageStats(key.id, 30);
        return {
          id: key.id,
          name: key.name,
          isActive: key.is_active,
          rateLimit: key.rate_limit_per_hour,
          allowedEndpoints: key.allowed_endpoints,
          usageCount: key.usage_count || 0,
          lastUsed: key.last_used_at,
          expiresAt: key.expires_at,
          createdAt: key.created_at,
          last30Days: {
            requests: stats.totalRequests,
            cost: stats.totalCost,
            cacheHitRate: stats.cacheHitRate,
            avgResponseTime: stats.avgResponseTime
          }
        };
      })
    );

    res.json({
      success: true,
      data: apiKeysWithStats,
      meta: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        timestamp: new Date().toISOString()
      }
    });
  })
);

/**
 * PUT /api/v1/admin/api-keys/:id
 * Update API key settings
 */
router.put('/api-keys/:id',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateSchema = z.object({
      name: z.string().optional(),
      isActive: z.boolean().optional(),
      rateLimit: z.number().min(1).max(10000).optional(),
      allowedEndpoints: z.array(z.string()).optional(),
      expiresAt: z.string().optional()
    });

    const validation = updateSchema.safeParse(req.body);
    if (!validation.success) {
      throw new ValidationError('Invalid update data', validation.error.errors);
    }

    const updates: any = {};
    if (validation.data.name) updates.name = validation.data.name;
    if (validation.data.isActive !== undefined) updates.is_active = validation.data.isActive;
    if (validation.data.rateLimit) updates.rate_limit_per_hour = validation.data.rateLimit;
    if (validation.data.allowedEndpoints) updates.allowed_endpoints = validation.data.allowedEndpoints;
    if (validation.data.expiresAt) updates.expires_at = validation.data.expiresAt;

    const { data, error } = await supabase
      .from('api_keys')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error('Failed to update API key');
    }

    logger.info(`🔧 API key updated by admin: ${data.name} (${id})`);

    res.json({
      success: true,
      data: {
        id: data.id,
        name: data.name,
        isActive: data.is_active,
        rateLimit: data.rate_limit_per_hour,
        allowedEndpoints: data.allowed_endpoints,
        expiresAt: data.expires_at,
        updatedAt: new Date().toISOString()
      },
      message: 'API key updated successfully'
    });
  })
);

/**
 * DELETE /api/v1/admin/api-keys/:id
 * Deactivate an API key
 */
router.delete('/api-keys/:id',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('api_keys')
      .update({ is_active: false })
      .eq('id', id)
      .select('name')
      .single();

    if (error) {
      throw new Error('Failed to deactivate API key');
    }

    logger.info(`🗑️ API key deactivated by admin: ${data.name} (${id})`);

    res.json({
      success: true,
      message: 'API key deactivated successfully'
    });
  })
);

/**
 * POST /api/v1/admin/cache/clear
 * Clear cache entries
 */
router.post('/cache/clear',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const { topicKey, jurisdiction } = req.body;

    if (topicKey) {
      await cacheService.invalidateCache(topicKey, jurisdiction);
      logger.info(`🧹 Cache cleared by admin: ${topicKey}${jurisdiction ? ` in ${jurisdiction}` : ''}`);
    } else {
      // Clear expired entries
      const deletedCount = await cacheService.clearExpiredEntries();
      logger.info(`🧹 Expired cache entries cleared by admin: ${deletedCount} entries`);
    }

    res.json({
      success: true,
      message: topicKey 
        ? `Cache cleared for ${topicKey}${jurisdiction ? ` in ${jurisdiction}` : ''}`
        : 'Expired cache entries cleared'
    });
  })
);

/**
 * GET /api/v1/admin/usage/export
 * Export usage data for analysis
 */
router.get('/usage/export',
  authenticateApiKey,
  requireAdmin,
  asyncHandler(async (req: Request, res: Response) => {
    const days = parseInt(req.query.days as string) || 30;
    const format = req.query.format as string || 'json';

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data: usage, error } = await supabase
      .from('municipal_api_usage')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error('Failed to export usage data');
    }

    if (format === 'csv') {
      // Convert to CSV format
      const headers = [
        'timestamp', 'api_key_id', 'endpoint', 'method', 'address', 'query',
        'jurisdiction', 'topic_key', 'cache_hit', 'response_time_ms',
        'confidence_score', 'sources_count', 'cost_usd', 'status_code'
      ];

      const csvData = [
        headers.join(','),
        ...(usage || []).map(row => [
          row.created_at,
          row.api_key_id,
          row.endpoint,
          row.request_method,
          row.address || '',
          row.query || '',
          row.jurisdiction || '',
          row.topic_key || '',
          row.cache_hit,
          row.response_time_ms || '',
          row.confidence_score || '',
          row.sources_count || 0,
          row.cost_usd || 0,
          row.status_code
        ].join(','))
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="usage-export-${days}days.csv"`);
      res.send(csvData);
    } else {
      res.json({
        success: true,
        data: usage || [],
        meta: {
          period: `${days} days`,
          count: usage?.length || 0,
          exportedAt: new Date().toISOString()
        }
      });
    }
  })
);

export { router as adminRoutes };
