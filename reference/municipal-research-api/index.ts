import express from 'express';
import cors from 'cors';
import { config, validateConfig } from './config.js';
import { getJurisdiction } from './services/geocoding.js';
import { findMunicipalSources } from './services/search.js';
import { extractContent } from './services/contentExtractor.js';
import { generateAnswer, generateFallbackAnswer } from './services/aiAnalyzer.js';
import { knowledgeCache } from './services/cache.js';
import { mcpResearcher } from './services/mcpResearcher.js';

// Validate configuration
const configValid = validateConfig();
if (!configValid) {
  console.log('⚠️ API will run in limited mode without full functionality');
}

const app = express();
const port = config.server.port;

app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Cache stats endpoint
app.get('/api/cache/stats', async (req, res) => {
  try {
    const stats = await knowledgeCache.getCacheStats();
    res.json({ success: true, stats });
  } catch (error) {
    res.status(500).json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// Main research endpoint
app.post('/api/research', async (req, res) => {
  try {
    const { address, query } = req.body;

    if (!address || !query) {
      return res.status(400).json({
        success: false,
        error: 'Both address and query are required'
      });
    }

    console.log(`🔍 Research request: "${query}" for ${address}`);
    const startTime = Date.now();

    // Step 1: Identify jurisdiction from address
    const jurisdiction = await getJurisdiction(address);
    console.log(`🏛️ Identified jurisdiction: ${jurisdiction}`);

    // Step 2: Check cache first
    const cachedResult = await knowledgeCache.findCached(query, jurisdiction);
    if (cachedResult) {
      const response = {
        success: true,
        address,
        query,
        jurisdiction,
        answer: cachedResult.answer,
        sources: cachedResult.sources,
        confidence: cachedResult.confidence,
        cached: true,
        cacheAge: cachedResult.cacheAge,
        processingTimeMs: Date.now() - startTime
      };

      console.log(`✅ Returned cached result in ${response.processingTimeMs}ms`);
      return res.json(response);
    }

    // Step 3: Perform fresh research
    const researchResult = await performMunicipalResearch(jurisdiction, query);

    // Step 4: Cache the result
    await knowledgeCache.store(query, jurisdiction, researchResult);

    const response = {
      success: true,
      address,
      query,
      jurisdiction,
      answer: researchResult.answer,
      sources: researchResult.sources,
      confidence: researchResult.confidence,
      method: researchResult.method || 'unknown',
      cached: false,
      processingTimeMs: Date.now() - startTime
    };

    console.log(`✅ Research completed in ${response.processingTimeMs}ms`);
    res.json(response);

  } catch (error) {
    console.error('❌ Research failed:', error);
    res.status(500).json({
      success: false,
      error: 'Research failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

async function performMunicipalResearch(jurisdiction: string, query: string): Promise<{
  answer: string;
  sources: string[];
  confidence: number;
  tokensUsed?: number;
  method?: string;
}> {
  console.log(`🔬 Performing municipal research for "${query}" in ${jurisdiction}`);

  // Check if we have MCP API keys for enhanced research
  const hasMCPKeys = process.env.PERPLEXITY_API_KEY && process.env.OPENROUTER_API_KEY;

  if (hasMCPKeys) {
    console.log('🚀 Using MCP-powered research for maximum accuracy');
    try {
      const mcpResult = await mcpResearcher.performMunicipalResearch(jurisdiction, query);
      console.log(`✅ MCP research completed with confidence: ${mcpResult.confidence}`);
      return mcpResult;
    } catch (error) {
      console.warn('⚠️ MCP research failed, falling back to traditional method:', error instanceof Error ? error.message : 'Unknown error');
      // Fall through to traditional method
    }
  } else {
    console.log('📝 MCP API keys not configured, using traditional research method');
  }

  // Traditional research method (fallback)
  try {
    // Step 1: Find municipal sources
    const sources = await findMunicipalSources(jurisdiction, query);

    if (sources.length === 0) {
      console.log('⚠️ No municipal sources found, generating fallback answer');
      return {
        answer: generateFallbackAnswer(query, jurisdiction),
        sources: [],
        confidence: 0.1,
        method: 'fallback'
      };
    }

    // Step 2: Extract content from sources
    const extractedContent = await extractContent(sources);

    if (extractedContent.length === 0) {
      console.log('⚠️ No content extracted, generating fallback answer');
      return {
        answer: generateFallbackAnswer(query, jurisdiction),
        sources: sources.map((s: any) => s.link),
        confidence: 0.2,
        method: 'traditional_no_content'
      };
    }

    // Step 3: Generate AI analysis
    const result = await generateAnswer(query, jurisdiction, extractedContent);

    console.log(`✅ Traditional research completed with ${result.sources.length} sources, confidence: ${result.confidence}`);
    return {
      ...result,
      method: 'traditional'
    };

  } catch (error) {
    console.error('❌ Municipal research failed:', error);

    // Return fallback answer instead of throwing
    return {
      answer: generateFallbackAnswer(query, jurisdiction),
      sources: [],
      confidence: 0.1,
      method: 'error_fallback'
    };
  }
}

// Initialize cache on startup
knowledgeCache.initialize().catch(error => {
  console.error('❌ Failed to initialize cache:', error);
});

app.listen(port, () => {
  console.log(`🚀 Municipal Research API running on port ${port}`);
  console.log(`📚 Ready for REAL municipal research with Google APIs and OpenAI`);
  console.log(`💾 Cache system initialized`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
});
